('E:\\成都项目\\项目开发相关代码\\万象网站会员数据导出\\build\\db_export_simple\\PYZ-00.pyz',
 [('multiprocessing.popen_fork',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('threading', 'c:\\program files\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python\\lib\\selectors.py', 'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'c:\\program files\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python\\lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'c:\\program files\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'c:\\program files\\python\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python\\lib\\gettext.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'c:\\program files\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'c:\\program files\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('ntpath', 'c:\\program files\\python\\lib\\ntpath.py', 'PYMODULE'),
  ('string', 'c:\\program files\\python\\lib\\string.py', 'PYMODULE'),
  ('genericpath', 'c:\\program files\\python\\lib\\genericpath.py', 'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'c:\\program files\\python\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('email', 'c:\\program files\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'c:\\program files\\python\\lib\\calendar.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'c:\\program files\\python\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'c:\\program files\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python\\lib\\pprint.py', 'PYMODULE'),
  ('doctest', 'c:\\program files\\python\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'c:\\program files\\python\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python\\lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('contextvars', 'c:\\program files\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('typing', 'c:\\program files\\python\\lib\\typing.py', 'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'c:\\program files\\python\\lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'c:\\program files\\python\\lib\\pdb.py', 'PYMODULE'),
  ('getopt', 'c:\\program files\\python\\lib\\getopt.py', 'PYMODULE'),
  ('pydoc', 'c:\\program files\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('email.message',
   'c:\\program files\\python\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('quopri', 'c:\\program files\\python\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'c:\\program files\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python\\lib\\optparse.py', 'PYMODULE'),
  ('http.server',
   'c:\\program files\\python\\lib\\http\\server.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('html', 'c:\\program files\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'c:\\program files\\python\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'c:\\program files\\python\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'c:\\program files\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'c:\\program files\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('lib2to3.refactor',
   'c:\\program files\\python\\lib\\lib2to3\\refactor.py',
   'PYMODULE'),
  ('lib2to3.btm_matcher',
   'c:\\program files\\python\\lib\\lib2to3\\btm_matcher.py',
   'PYMODULE'),
  ('lib2to3.btm_utils',
   'c:\\program files\\python\\lib\\lib2to3\\btm_utils.py',
   'PYMODULE'),
  ('lib2to3.pgen2.grammar',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\grammar.py',
   'PYMODULE'),
  ('lib2to3.pygram',
   'c:\\program files\\python\\lib\\lib2to3\\pygram.py',
   'PYMODULE'),
  ('lib2to3.pytree',
   'c:\\program files\\python\\lib\\lib2to3\\pytree.py',
   'PYMODULE'),
  ('lib2to3',
   'c:\\program files\\python\\lib\\lib2to3\\__init__.py',
   'PYMODULE'),
  ('lib2to3.patcomp',
   'c:\\program files\\python\\lib\\lib2to3\\patcomp.py',
   'PYMODULE'),
  ('lib2to3.pgen2.parse',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\parse.py',
   'PYMODULE'),
  ('lib2to3.pgen2.literals',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\literals.py',
   'PYMODULE'),
  ('lib2to3.fixer_util',
   'c:\\program files\\python\\lib\\lib2to3\\fixer_util.py',
   'PYMODULE'),
  ('lib2to3.pgen2.token',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\token.py',
   'PYMODULE'),
  ('lib2to3.pgen2.tokenize',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\tokenize.py',
   'PYMODULE'),
  ('lib2to3.pgen2.driver',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\driver.py',
   'PYMODULE'),
  ('lib2to3.pgen2.pgen',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\pgen.py',
   'PYMODULE'),
  ('lib2to3.pgen2',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('platform', 'c:\\program files\\python\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'c:\\program files\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python\\lib\\zipimport.py', 'PYMODULE'),
  ('runpy', 'c:\\program files\\python\\lib\\runpy.py', 'PYMODULE'),
  ('shlex', 'c:\\program files\\python\\lib\\shlex.py', 'PYMODULE'),
  ('glob', 'c:\\program files\\python\\lib\\glob.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'c:\\program files\\python\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'c:\\program files\\python\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'c:\\program files\\python\\lib\\cmd.py', 'PYMODULE'),
  ('inspect', 'c:\\program files\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'c:\\program files\\python\\lib\\ast.py', 'PYMODULE'),
  ('token', 'c:\\program files\\python\\lib\\token.py', 'PYMODULE'),
  ('difflib', 'c:\\program files\\python\\lib\\difflib.py', 'PYMODULE'),
  ('__future__', 'c:\\program files\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('bisect', 'c:\\program files\\python\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'c:\\program files\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.header',
   'c:\\program files\\python\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python\\lib\\email\\errors.py',
   'PYMODULE'),
  ('tokenize', 'c:\\program files\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('posixpath', 'c:\\program files\\python\\lib\\posixpath.py', 'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'c:\\program files\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'c:\\program files\\python\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'c:\\program files\\python\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('stat', 'c:\\program files\\python\\lib\\stat.py', 'PYMODULE'),
  ('_compression',
   'c:\\program files\\python\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'c:\\program files\\python\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'c:\\program files\\python\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'c:\\program files\\python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python\\lib\\numbers.py', 'PYMODULE'),
  ('base64', 'c:\\program files\\python\\lib\\base64.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes', 'c:\\program files\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'c:\\program files\\python\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('tempfile', 'c:\\program files\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('struct', 'c:\\program files\\python\\lib\\struct.py', 'PYMODULE'),
  ('socket', 'c:\\program files\\python\\lib\\socket.py', 'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('test.support',
   'c:\\program files\\python\\lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'c:\\program files\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\program files\\python\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('test.support.testresult',
   'c:\\program files\\python\\lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('nntplib', 'c:\\program files\\python\\lib\\nntplib.py', 'PYMODULE'),
  ('logging.handlers',
   'c:\\program files\\python\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('smtplib', 'c:\\program files\\python\\lib\\smtplib.py', 'PYMODULE'),
  ('test', 'c:\\program files\\python\\lib\\test\\__init__.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('subprocess', 'c:\\program files\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('_strptime', 'c:\\program files\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'c:\\program files\\python\\lib\\datetime.py', 'PYMODULE'),
  ('csv', 'c:\\program files\\python\\lib\\csv.py', 'PYMODULE'),
  ('getpass', 'c:\\program files\\python\\lib\\getpass.py', 'PYMODULE'),
  ('configparser',
   'c:\\program files\\python\\lib\\configparser.py',
   'PYMODULE'),
  ('os', 'c:\\program files\\python\\lib\\os.py', 'PYMODULE')])
