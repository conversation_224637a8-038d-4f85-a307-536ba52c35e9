@echo off
echo ===================================================
echo      数据库导出工具打包程序 (直接命令行版)
echo ===================================================
echo.
echo 此批处理文件将使用 PyInstaller 打包数据库导出工具
echo 打包过程需要几分钟时间，请耐心等待...
echo.
echo 按任意键开始打包...
pause > nul

echo.
echo 正在执行打包...

python -m PyInstaller ^
--onefile ^
--console ^
--clean ^
--name "数据库导出工具" ^
--add-data "config.ini;." ^
--hidden-import=pyodbc ^
--hidden-import=configparser ^
--hidden-import=csv ^
--hidden-import=datetime ^
"db_export_simple.py"

echo.
if %ERRORLEVEL% EQU 0 (
    echo 打包成功完成！
    echo 打包后的文件位于 dist 目录中
    echo.
    echo 正在复制配置文件...
    copy config.ini dist\config.ini
    echo.
    echo 全部完成！
) else (
    echo 打包过程中出现错误，请查看上方日志。
)

echo.
echo 按任意键退出...
pause > nul