@echo off
echo 简单打包方案
echo =============

echo 正在清理...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo.
echo 正在打包（包含所有依赖）...
pyinstaller --onefile --console --name "数据库导出工具" --add-data "config.ini;." db_export_tool.py

if errorlevel 1 (
    echo 打包失败，尝试备用方案...
    echo 使用 --noupx 参数重新打包...
    pyinstaller --onefile --console --noupx --name "数据库导出工具" db_export_tool.py
)

echo.
echo 复制配置文件...
copy "config.ini" "dist\config.ini"

echo.
echo 完成！请测试运行 dist\数据库导出工具.exe
pause
