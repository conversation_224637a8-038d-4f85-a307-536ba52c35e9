# CSV-JSON数据处理工具使用说明

## 功能概述
这个工具可以读取CSV文件和JSON文件，根据openid匹配数据，生成新格式的CSV文件。

## 文件说明

### 主要文件
- `csv_json_processor.py` - 主程序
- `processor_config.ini` - 配置文件
- `build_processor.bat` - 打包脚本

### 示例文件
- `sample_data.csv` - 示例CSV文件
- `sample_users.json` - 示例JSON文件

## 输入文件格式

### CSV文件格式
CSV文件必须包含以下4列（第一行为列名）：
```
snameshow,scardidshow,sDis,sopenid
张*三,*123456,普通会员,abc123def456
李*四,*789012,黄金会员,def789ghi012
```

- `snameshow`: 姓名
- `scardidshow`: 证件号码
- `sDis`: 会员等级（可选，根据配置决定是否使用）
- `sopenid`: 用于匹配JSON数据的MD5字符串

### JSON文件格式
JSON文件必须包含 `all_users` 数组：
```json
{
  "all_users": [
    {
      "openid": "abc123def456",
      "grade_name": "钻石会员",
      "grow_points": "500",
      "curr_points": "1200",
      "curr_wallet": 150.75,
      "curr_barMoney": 88.50
    }
  ]
}
```

## 输出文件格式

生成的CSV文件包含8列：
- 门店账户: 配置文件中设置的固定值
- 姓名: 来自CSV的snameshow列
- 证件号码: 来自CSV的scardidshow列
- 会员等级: 根据配置来源（CSV的sDis或JSON的grade_name）
- 等级积分: JSON中的grow_points（整数）
- 可用积分: JSON中的curr_points（整数）
- 本金: JSON中的curr_wallet（保留2位小数）
- 赠费: JSON中的curr_barMoney（保留2位小数）

## 配置文件说明

`config.ini` 文件配置项：

```ini
[SETTINGS]
# 门店账户（固定值）
store_account = dgdxfwk

# 会员等级数据来源: csv 或 json
grade_source = json

# 默认会员等级
default_grade = 普通会员
```

### 配置说明
- `store_account`: 输出CSV中"门店账户"列的固定值
- `grade_source`: 
  - `csv`: 使用输入CSV文件中的sDis列作为会员等级
  - `json`: 使用JSON文件中的grade_name字段作为会员等级
- `default_grade`: 当找不到等级信息时使用的默认等级

## 使用步骤

### 方法1: 直接运行Python脚本
1. 确保Python已安装
2. 将CSV和JSON文件放在程序同一目录
3. 运行: `python csv_json_processor.py`
4. 按提示选择CSV和JSON文件
5. 程序会生成新的CSV文件

### 方法2: 使用EXE文件
1. 运行 `build_processor.bat` 打包程序
2. 将生成的EXE文件和config.ini放在同一目录
3. 将要处理的CSV和JSON文件放在同一目录
4. 双击运行EXE文件
5. 按提示操作

## 输出文件命名

输出文件命名格式: `{门店账户}_{时间戳}.csv`
例如: `dgdxfwk_20241202_143025.csv`

## 数据匹配逻辑

1. 程序读取CSV文件中的每一行数据
2. 使用sopenid列的值在JSON文件的all_users中查找匹配项
3. 如果找到匹配：
   - 从JSON中提取所需的5个字段值
   - 按配置决定使用CSV还是JSON中的等级信息
4. 如果未找到匹配：
   - 使用默认值（积分为0，金额为0.00）
   - 等级使用CSV中的sDis或默认等级

## 错误处理

- 如果数值字段无法转换，会使用默认值（0或0.00）
- 如果找不到匹配的用户，会在控制台显示警告
- 程序会显示处理进度和结果统计

## 注意事项

1. CSV文件必须使用UTF-8编码
2. JSON文件必须是有效的JSON格式
3. openid字段用于匹配，必须完全一致
4. 数值字段会自动处理类型转换
5. 生成的文件使用UTF-8-BOM编码，Excel可以正确打开

## 示例运行

```
CSV和JSON数据处理工具 v1.0
============================================================
✅ 配置文件加载成功

📁 当前目录下的 CSV 文件:
  1. sample_data.csv

请选择 CSV 文件 (输入序号): 1
✅ 已选择: sample_data.csv

📁 当前目录下的 JSON 文件:
  1. sample_users.json

请选择 JSON 文件 (输入序号): 1
✅ 已选择: sample_users.json

✅ CSV文件读取成功，共 3 条记录
✅ JSON文件读取成功，共 3 个用户记录
🔄 正在处理数据...
  ✅ 第1行: 张*三 - 找到匹配数据
  ✅ 第2行: 李*四 - 找到匹配数据
  ✅ 第3行: 王*五 - 找到匹配数据
✅ 数据处理完成，共处理 3 条记录
✅ 输出文件生成成功: dgdxfwk_20241202_143025.csv
📊 输出记录数: 3 条

🎉 程序执行完成！
```
