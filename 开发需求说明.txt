
需要开发一个油猴（Greasy Fork）插件，JS文件。
该插件主要用于Chrome游览器上，油猴（Greasy Fork）里面。

该插件的主要功能说明：
1、插件只在 https://qian.sicent.com/ 访问这个页面的时候生效。
2、插件主要目的是通过游览网页，提取网页上的会员数据内容。
3、使用插件时会先手动跳转到需要提取的会员信息列表网页，例如：https://qian.sicent.com/7.0/?token=92b6f3c2-4a10-40a5-bf7c-b7aee5549bab#/customer/member/list
4、插件需要再网页的界面下方提供按钮《导出会员列表》的按键功能。
5、点击《导出会员列表》按键后，首先获取当前网页的源代码，找到页码部分的内容例如：
<button type="button" class="btn-prev" disabled="disabled"><span>&lt;上一页</span></button>
<ul class="el-pager"><li class="number active">1</li><!----><li class="number">2</li><li class="number">3</li><li class="number">4</li><li class="number">5</li><li class="number">6</li><li class="el-icon more btn-quicknext el-icon-more"></li><li class="number">799</li></ul>
<button type="button" class="btn-next"><span>下一页&gt;</span></button>
从这个信息我们可以看到，当前页数为 1，总页数为799。所以我们可以知道一共有799页。
6、提示我们输入需要导出数据开始页数字，结束页数字。 例如我想设置开始页为5，那么我会先跳转到5页，然后点《导出会员列表》，获取到当前网页中的内容后，在模拟点击《下一页》，标签进行跳转到下一页，在获取新的内容，同时识别新页面内容的页码数据，来确定当前指定到的页面。
7、获取的内容描述：

<tr class="el-table__row"><td rowspan="1" colspan="1" class="el-table_2_column_12   el-table__cell"><div class="cell el-tooltip" style="width: 93px;">dgdxfwk</div></td><td rowspan="1" colspan="1" class="el-table_2_column_13   el-table__cell"><div class="cell">*211212</div></td><td rowspan="1" colspan="1" class="el-table_2_column_14   el-table__cell"><div class="cell">*211212</div></td><td rowspan="1" colspan="1" class="el-table_2_column_15   el-table__cell"><div class="cell">刘*泽</div></td><td rowspan="1" colspan="1" class="el-table_2_column_16   el-table__cell"><div class="cell">普通会员</div></td><td rowspan="1" colspan="1" class="el-table_2_column_17   el-table__cell"><div class="cell">正常</div></td><td rowspan="1" colspan="1" class="el-table_2_column_18   el-table__cell"><div class="cell">1</div></td><td rowspan="1" colspan="1" class="el-table_2_column_19   el-table__cell"><div class="cell">1</div></td><td rowspan="1" colspan="1" class="el-table_2_column_20   el-table__cell"><div class="cell">2025-09-02 10:34:07</div></td><td rowspan="1" colspan="1" class="el-table_2_column_21   el-table__cell"><div class="cell">2025-09-02 10:34:07</div></td><td rowspan="1" colspan="1" class="el-table_2_column_22   is-hidden el-table__cell"><div class="cell"><a data-v-8dd35b7e="" href="#/customer/member/detail?custCardId=JN5RBYwoKj4%2BUWJ9giJhFjglfw%2FXkKV1hF7gmbT3eveHiF4cGyyy%2Fw%3D%3D&amp;snbid=dgdxfwk&amp;istatus=1" class=""><button data-v-8dd35b7e="" type="button" class="el-button el-button--text"><!----><!----><span><i data-v-8dd35b7e="" class="el-tooltip icon iconfont item" aria-describedby="el-tooltip-1299" tabindex="0"></i></span></button></a></div></td></tr>

<tr class="el-table__row"><td rowspan="1" colspan="1" class="el-table_2_column_12   el-table__cell"><div class="cell el-tooltip" style="width: 93px;">dgdxfwk</div></td><td rowspan="1" colspan="1" class="el-table_2_column_13   el-table__cell"><div class="cell">*170195</div></td><td rowspan="1" colspan="1" class="el-table_2_column_14   el-table__cell"><div class="cell">*170195</div></td><td rowspan="1" colspan="1" class="el-table_2_column_15   el-table__cell"><div class="cell">张*海</div></td><td rowspan="1" colspan="1" class="el-table_2_column_16   el-table__cell"><div class="cell">普通会员</div></td><td rowspan="1" colspan="1" class="el-table_2_column_17   el-table__cell"><div class="cell">正常</div></td><td rowspan="1" colspan="1" class="el-table_2_column_18   el-table__cell"><div class="cell">1</div></td><td rowspan="1" colspan="1" class="el-table_2_column_19   el-table__cell"><div class="cell">1</div></td><td rowspan="1" colspan="1" class="el-table_2_column_20   el-table__cell"><div class="cell">2025-09-02 10:33:39</div></td><td rowspan="1" colspan="1" class="el-table_2_column_21   el-table__cell"><div class="cell">2025-09-02 10:33:39</div></td><td rowspan="1" colspan="1" class="el-table_2_column_22   is-hidden el-table__cell"><div class="cell"><a data-v-8dd35b7e="" href="#/customer/member/detail?custCardId=76w10B8Xh1KNxfMTwvgMentX6N%2FX3NFZegrKMOwbTnjL5FKseNf7YA%3D%3D&amp;snbid=dgdxfwk&amp;istatus=1" class=""><button data-v-8dd35b7e="" type="button" class="el-button el-button--text"><!----><!----><span><i data-v-8dd35b7e="" class="el-tooltip icon iconfont item" aria-describedby="el-tooltip-8612" tabindex="0"></i></span></button></a></div></td></tr>

上面的内容是 2行会员数据，我需要的内容如下：
网吧账户：dgdxfwk
卡号：*211212
证件号：*211212
会员姓名：刘*泽
会员等级：普通会员
等级积分：1
可用积分：1


网吧账户：dgdxfwk
卡号：*170195
证件号：*170195
会员姓名：张*海
会员等级：普通会员
等级积分：1
可用积分：1

这就是我需要从网页中获取的会员信息。
最后我希望是导出为 excel表格  和 json 文件数据。

8、在执行导出的过程中，我希望有进度，显示当前已经处理的页数，获取到的会员数据数量。
9、完成后直接提供excel表格文件 和 JSON文件下载。

这就是我的功能需求，最终我需要的是一个在油猴插件中运行的JS代码，来实现我对网页数据的抓取功能。我认为这过程重点就是在页面的切换，不断切换下一页的操作上。能够成功切换下一页或者跳转页面，就可以顺利的获取到数据。