#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV和JSON数据处理工具
读取CSV文件和JSON文件，匹配数据并生成新的CSV文件
"""

import os
import csv
import json
import configparser
from datetime import datetime

class CSVJSONProcessor:
    def __init__(self):
        self.config = None
        self.csv_data = []
        self.json_data = {}
        self.output_data = []
        
    def load_config(self):
        """加载配置文件"""
        try:
            self.config = configparser.ConfigParser()
            config_file = 'config.ini'
            
            if not os.path.exists(config_file):
                # 创建默认配置文件
                self.create_default_config(config_file)
            
            self.config.read(config_file, encoding='utf-8')
            print("✅ 配置文件加载成功")
            return True
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def create_default_config(self, config_file):
        """创建默认配置文件"""
        config = configparser.ConfigParser()
        config.add_section('SETTINGS')
        config.set('SETTINGS', 'store_account', 'dgdxfwk')
        config.set('SETTINGS', 'grade_source', 'json')  # csv 或 json
        config.set('SETTINGS', 'default_grade', '普通会员')
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        print("✅ 已创建默认配置文件 config.ini")
    
    def list_files(self, extension):
        """列举指定扩展名的文件"""
        files = []
        for file in os.listdir('.'):
            if file.lower().endswith(extension.lower()):
                files.append(file)
        return files
    
    def select_file(self, files, file_type):
        """选择文件"""
        if not files:
            print(f"❌ 当前目录下没有找到 {file_type} 文件")
            return None
        
        print(f"\n📁 当前目录下的 {file_type} 文件:")
        for i, file in enumerate(files, 1):
            print(f"  {i}. {file}")
        
        while True:
            try:
                choice = input(f"\n请选择 {file_type} 文件 (输入序号): ").strip()
                if not choice:
                    continue
                
                index = int(choice) - 1
                if 0 <= index < len(files):
                    selected_file = files[index]
                    print(f"✅ 已选择: {selected_file}")
                    return selected_file
                else:
                    print("❌ 序号超出范围，请重新输入")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n程序被用户中断")
                return None
    
    def read_csv_file(self, csv_file):
        """读取CSV文件"""
        try:
            self.csv_data = []
            with open(csv_file, 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    self.csv_data.append(row)
            
            print(f"✅ CSV文件读取成功，共 {len(self.csv_data)} 条记录")
            
            # 显示前几条数据作为预览
            if self.csv_data:
                print("📋 数据预览:")
                for i, row in enumerate(self.csv_data[:3], 1):
                    print(f"  第{i}行: {dict(row)}")
                if len(self.csv_data) > 3:
                    print(f"  ... 还有 {len(self.csv_data) - 3} 条记录")
            
            return True
        except Exception as e:
            print(f"❌ CSV文件读取失败: {e}")
            return False
    
    def read_json_file(self, json_file):
        """读取JSON文件"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查JSON结构
            if 'all_users' in data:
                self.json_data = data['all_users']
                print(f"✅ JSON文件读取成功，共 {len(self.json_data)} 个用户记录")
            else:
                print("⚠️  JSON文件中未找到 'all_users' 字段，尝试直接使用根数据")
                self.json_data = data
            
            return True
        except Exception as e:
            print(f"❌ JSON文件读取失败: {e}")
            return False
    
    def find_user_data(self, openid):
        """根据openid查找用户数据"""
        try:
            # 如果json_data是列表
            if isinstance(self.json_data, list):
                for user in self.json_data:
                    if isinstance(user, dict) and user.get('openid') == openid:
                        return user
            
            # 如果json_data是字典
            elif isinstance(self.json_data, dict):
                for key, user in self.json_data.items():
                    if isinstance(user, dict) and user.get('openid') == openid:
                        return user
            
            return None
        except Exception as e:
            print(f"⚠️  查找用户数据时出错: {e}")
            return None
    
    def process_data(self):
        """处理数据，生成输出数据"""
        try:
            self.output_data = []
            store_account = self.config.get('SETTINGS', 'store_account')
            grade_source = self.config.get('SETTINGS', 'grade_source')
            default_grade = self.config.get('SETTINGS', 'default_grade')
            
            print("🔄 正在处理数据...")
            
            for i, row in enumerate(self.csv_data, 1):
                # 从CSV获取基本信息
                name = row.get('snameshow', '').strip()
                card_id = row.get('scardidshow', '').strip()
                dis = row.get('sDis', '').strip()
                openid = row.get('sopenid', '').strip()
                
                # 查找JSON中的用户数据
                user_data = self.find_user_data(openid)
                
                # 构建输出行数据
                output_row = {
                    '门店账户': store_account,
                    '姓名': name,
                    '证件号码': card_id,
                    '会员等级': '',
                    '等级积分': '0',
                    '可用积分': '0',
                    '本金': '0.00',
                    '赠费': '0.00'
                }
                
                if user_data:
                    # 会员等级
                    if grade_source.lower() == 'json':
                        output_row['会员等级'] = user_data.get('grade_name', default_grade)
                    else:
                        output_row['会员等级'] = dis if dis else default_grade
                    
                    # 等级积分
                    grow_points = user_data.get('grow_points', '0')
                    try:
                        output_row['等级积分'] = str(int(float(str(grow_points))))
                    except:
                        output_row['等级积分'] = '0'
                    
                    # 可用积分
                    curr_points = user_data.get('curr_points', '0')
                    try:
                        output_row['可用积分'] = str(int(float(str(curr_points))))
                    except:
                        output_row['可用积分'] = '0'
                    
                    # 本金
                    curr_wallet = user_data.get('curr_wallet', 0)
                    try:
                        output_row['本金'] = f"{float(curr_wallet):.2f}"
                    except:
                        output_row['本金'] = '0.00'
                    
                    # 赠费
                    curr_bar_money = user_data.get('curr_barMoney', 0)
                    try:
                        output_row['赠费'] = f"{float(curr_bar_money):.2f}"
                    except:
                        output_row['赠费'] = '0.00'
                    
                    print(f"  ✅ 第{i}行: {name} - 找到匹配数据")
                else:
                    # 没有找到匹配数据，使用默认值
                    output_row['会员等级'] = dis if dis else default_grade
                    print(f"  ⚠️  第{i}行: {name} - 未找到匹配数据，使用默认值")
                
                self.output_data.append(output_row)
            
            print(f"✅ 数据处理完成，共处理 {len(self.output_data)} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 数据处理失败: {e}")
            return False
    
    def write_output_csv(self):
        """写入输出CSV文件"""
        try:
            store_account = self.config.get('SETTINGS', 'store_account')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{store_account}_{timestamp}.csv"
            
            fieldnames = ['门店账户', '姓名', '证件号码', '会员等级', '等级积分', '可用积分', '本金', '赠费']
            
            with open(output_filename, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.output_data)
            
            print(f"✅ 输出文件生成成功: {output_filename}")
            print(f"📊 输出记录数: {len(self.output_data)} 条")
            
            return True
        except Exception as e:
            print(f"❌ 输出文件生成失败: {e}")
            return False
    
    def run(self):
        """主程序运行"""
        print("=" * 60)
        print("    CSV和JSON数据处理工具 v1.0")
        print("=" * 60)
        
        # 1. 加载配置
        if not self.load_config():
            return False
        
        # 2. 选择CSV文件
        csv_files = self.list_files('.csv')
        csv_file = self.select_file(csv_files, 'CSV')
        if not csv_file:
            return False
        
        # 3. 选择JSON文件
        json_files = self.list_files('.json')
        json_file = self.select_file(json_files, 'JSON')
        if not json_file:
            return False
        
        # 4. 读取CSV文件
        if not self.read_csv_file(csv_file):
            return False
        
        # 5. 读取JSON文件
        if not self.read_json_file(json_file):
            return False
        
        # 6. 处理数据
        if not self.process_data():
            return False
        
        # 7. 生成输出文件
        if not self.write_output_csv():
            return False
        
        return True

def main():
    """主函数"""
    processor = CSVJSONProcessor()
    
    try:
        success = processor.run()
        if success:
            print("\n🎉 程序执行完成！")
        else:
            print("\n❌ 程序执行失败！")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
