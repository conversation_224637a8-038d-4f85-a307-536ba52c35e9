@echo off
echo 正在清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "数据库导出工具.spec" del "数据库导出工具.spec"

echo.
echo 检查依赖库是否已安装...
python -c "import pyodbc, pandas, openpyxl; print('所有依赖库已安装')" 2>nul
if errorlevel 1 (
    echo 错误: 缺少必要的依赖库
    echo 正在安装依赖库...
    pip install pyodbc pandas openpyxl pyinstaller
)

echo.
echo 正在打包程序为EXE文件（包含所有依赖）...
pyinstaller --onefile --console --name "数据库导出工具" ^
    --hidden-import=pyodbc ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    --hidden-import=configparser ^
    --hidden-import=getpass ^
    --hidden-import=datetime ^
    --collect-all=pyodbc ^
    --collect-all=pandas ^
    --collect-all=openpyxl ^
    db_export_tool.py

echo.
echo 正在复制配置文件...
copy "config.ini" "dist\config.ini"

echo.
echo 打包完成！
echo EXE文件位于 dist 目录中
echo config.ini 已自动复制到 dist 目录
echo.
echo 测试运行方法:
echo   cd dist
echo   .\数据库导出工具.exe
echo.
pause
