#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库导出工具 - 简化版
只使用标准库和必要的pyodbc，避免打包问题
"""

import sys
import os
import configparser
import getpass
import csv
from datetime import datetime

# 密码配置
STARTUP_PASSWORD = "qq616887"
EXPORT_PASSWORD = "9sheng"

# 检查pyodbc - 增强版本，帮助PyInstaller识别
try:
    import pyodbc
    print("✅ pyodbc 模块加载成功")
    # 强制引用pyodbc的关键组件，帮助PyInstaller打包
    _ = pyodbc.drivers
    _ = pyodbc.connect
    _ = pyodbc.Error
except ImportError as e:
    print("❌ 错误: 缺少 pyodbc 模块")
    print(f"详细错误: {e}")
    print("请安装: pip install pyodbc")
    print("如果是EXE版本，请重新打包程序")
    input("按回车键退出...")
    sys.exit(1)
except Exception as e:
    print(f"⚠️  pyodbc 加载警告: {e}")
    print("程序将继续运行...")

class SimpleDBExportTool:
    def __init__(self):
        self.config = None
        self.connection = None
        self.data = []
        self.replacement_rules = {}
        
    def get_config_path(self):
        """获取配置文件路径"""
        if getattr(sys, 'frozen', False):
            application_path = os.path.dirname(sys.executable)
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(application_path, 'config.ini')

    def load_config(self):
        """加载配置文件"""
        try:
            self.config = configparser.ConfigParser()
            config_path = self.get_config_path()
            
            print(f"正在查找配置文件: {config_path}")
            
            if not os.path.exists(config_path):
                print(f"❌ 配置文件不存在: {config_path}")
                return False
            
            self.config.read(config_path, encoding='utf-8')
            print("✅ 配置文件加载成功")
            
            self.load_replacement_rules()
            return True
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def load_replacement_rules(self):
        """加载字符替换规则"""
        try:
            if self.config.has_section('REPLACEMENTS'):
                for field_name, rule in self.config.items('REPLACEMENTS'):
                    if ',' in rule:
                        old_char, new_char = rule.split(',', 1)
                        self.replacement_rules[field_name] = {
                            'old': old_char.strip(),
                            'new': new_char.strip()
                        }
                        print(f"✅ 加载替换规则: {field_name} -> '{old_char.strip()}' 替换为 '{new_char.strip()}'")
                
                if self.replacement_rules:
                    print(f"✅ 共加载 {len(self.replacement_rules)} 个字符替换规则")
        except Exception as e:
            print(f"⚠️  加载替换规则失败: {e}")

    def verify_password(self, prompt, correct_password):
        """验证密码"""
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                password = getpass.getpass(prompt)
                if password == correct_password:
                    print("✅ 密码验证成功")
                    return True
                else:
                    remaining = max_attempts - attempt - 1
                    if remaining > 0:
                        print(f"❌ 密码错误，还有 {remaining} 次机会")
                    else:
                        print("❌ 密码错误次数过多")
            except KeyboardInterrupt:
                print("\n程序被用户中断")
                return False
        return False

    def connect_database(self):
        """连接数据库"""
        try:
            host = self.config.get('DATABASE', 'host')
            port = self.config.get('DATABASE', 'port')
            database = self.config.get('DATABASE', 'database')
            auth_type = self.config.get('DATABASE', 'auth_type')
            
            print(f"正在连接数据库: {host}:{port}/{database}")
            
            # 获取可用驱动程序
            available_drivers = pyodbc.drivers()
            print("系统中可用的ODBC驱动程序:")
            for i, driver in enumerate(available_drivers, 1):
                print(f"  {i}. {driver}")
            
            # 尝试多种驱动程序
            drivers = [
                "ODBC Driver 17 for SQL Server",
                "ODBC Driver 13 for SQL Server", 
                "SQL Server Native Client 11.0",
                "SQL Server"
            ]
            
            existing_drivers = [d for d in drivers if d in available_drivers]
            if not existing_drivers:
                print("❌ 未找到兼容的SQL Server ODBC驱动程序")
                return False

            for driver in existing_drivers:
                try:
                    if auth_type.lower() == 'windows':
                        connection_string = (
                            f"DRIVER={{{driver}}};"
                            f"SERVER={host},{port};"
                            f"DATABASE={database};"
                            f"Trusted_Connection=yes;"
                        )
                        print(f"尝试使用 Windows 认证，驱动: {driver}")
                    else:
                        username = self.config.get('DATABASE', 'username')
                        password = self.config.get('DATABASE', 'password')
                        connection_string = (
                            f"DRIVER={{{driver}}};"
                            f"SERVER={host},{port};"
                            f"DATABASE={database};"
                            f"UID={username};"
                            f"PWD={password};"
                        )
                        print(f"尝试使用 SQL Server 认证，驱动: {driver}")
                    
                    self.connection = pyodbc.connect(connection_string, timeout=10)
                    print(f"✅ 数据库连接成功，使用驱动: {driver}")
                    return True
                    
                except pyodbc.Error as e:
                    print(f"驱动 {driver} 连接失败: {e}")
                    continue
            
            print("❌ 所有可用驱动程序都无法连接数据库")
            return False
            
        except Exception as e:
            print(f"❌ 连接过程中发生错误: {e}")
            return False

    def read_data(self):
        """读取数据库数据"""
        try:
            table_name = self.config.get('DATABASE', 'table_name')
            export_fields = self.config.get('DATABASE', 'export_fields').split(',')
            export_fields = [field.strip() for field in export_fields]
            
            fields_str = ', '.join(export_fields)
            sql_query = f"SELECT {fields_str} FROM {table_name}"
            
            print(f"正在读取表: {table_name}")
            print(f"查询字段: {', '.join(export_fields)}")
            
            cursor = self.connection.cursor()
            cursor.execute(sql_query)
            rows = cursor.fetchall()
            
            # 转换为列表格式
            self.data = []
            for row in rows:
                record = {}
                for i, field in enumerate(export_fields):
                    record[field] = str(row[i]) if row[i] is not None else ''
                self.data.append(record)
            
            print(f"✅ 数据读取成功，共 {len(self.data)} 条记录")
            
            # 应用字符替换
            if self.replacement_rules:
                self.apply_replacements()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据读取失败: {e}")
            return False

    def apply_replacements(self):
        """应用字符替换规则"""
        try:
            print("正在应用字符替换规则...")
            replacement_count = 0
            
            for field_name, rule in self.replacement_rules.items():
                old_char = rule['old']
                new_char = rule['new']
                count = 0
                
                for record in self.data:
                    if field_name in record:
                        if old_char in record[field_name]:
                            record[field_name] = record[field_name].replace(old_char, new_char)
                            count += 1
                
                if count > 0:
                    print(f"✅ 字段 '{field_name}': 替换了 {count} 条记录中的 '{old_char}' -> '{new_char}'")
                    replacement_count += count
                else:
                    print(f"ℹ️  字段 '{field_name}': 未找到需要替换的数据")
            
            if replacement_count > 0:
                print(f"✅ 字符替换完成，共处理 {replacement_count} 个字段值")
                
        except Exception as e:
            print(f"❌ 字符替换失败: {e}")

    def export_to_csv(self):
        """导出数据到CSV文件"""
        try:
            output_filename = self.config.get('DATABASE', 'output_filename')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, _ = os.path.splitext(output_filename)
            csv_filename = f"{name}_{timestamp}.csv"
            
            # 确保输出文件在程序同一目录下
            if getattr(sys, 'frozen', False):
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(os.path.abspath(__file__))
            
            output_path = os.path.join(application_path, csv_filename)
            
            print(f"正在导出数据到: {csv_filename}")
            
            if self.data:
                fieldnames = list(self.data[0].keys())
                with open(output_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(self.data)
                
                print(f"✅ 数据导出成功！")
                print(f"📁 文件位置: {output_path}")
                print(f"📊 导出记录数: {len(self.data)} 条")
                return True
            else:
                print("❌ 没有数据可导出")
                return False
                
        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False

    def run(self):
        """主程序运行"""
        print("=" * 50)
        print("    数据库导出工具 v1.0 (简化版)")
        print("=" * 50)
        
        # 显示运行环境
        if getattr(sys, 'frozen', False):
            print("运行模式: EXE打包版本")
            print(f"程序路径: {sys.executable}")
        else:
            print("运行模式: Python脚本")
        print("-" * 50)
        
        # 1. 加载配置
        if not self.load_config():
            return False
        
        # 2. 验证启动密码
        if not self.verify_password("请输入启动密码: ", STARTUP_PASSWORD):
            return False
        
        # 3. 连接数据库
        if not self.connect_database():
            return False
        
        # 4. 读取数据
        if not self.read_data():
            self.connection.close()
            return False
        
        # 5. 验证导出密码
        print("\n准备导出数据，需要进行二次密码验证")
        if not self.verify_password("请输入导出密码: ", EXPORT_PASSWORD):
            self.connection.close()
            return False
        
        # 6. 导出数据
        success = self.export_to_csv()
        
        # 7. 关闭连接
        self.connection.close()
        print("数据库连接已关闭")
        
        return success

def main():
    """主函数"""
    tool = SimpleDBExportTool()
    
    try:
        success = tool.run()
        if success:
            print("\n🎉 程序执行完成！")
        else:
            print("\n❌ 程序执行失败！")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            input("\n按回车键退出...")
        except:
            import time
            print("程序将在5秒后自动退出...")
            time.sleep(5)

if __name__ == "__main__":
    main()
