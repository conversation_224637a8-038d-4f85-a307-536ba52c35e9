#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库导出工具
支持SQL Server数据库连接和数据导出到Excel
"""

import sys
import os
import configparser
import getpass
import pyodbc
import pandas as pd
from datetime import datetime

# ==================== 密码配置区域 ====================
# 程序启动密码
STARTUP_PASSWORD = "qq616887"

# 导出确认密码
EXPORT_PASSWORD = "9sheng"
# ====================================================

class DatabaseExportTool:
    def __init__(self):
        self.config = None
        self.connection = None
        self.data = None
        self.replacement_rules = {}
        
    def load_config(self):
        """加载配置文件"""
        try:
            self.config = configparser.ConfigParser()
            if not os.path.exists('config.ini'):
                print("❌ 配置文件 config.ini 不存在！")
                return False
            
            self.config.read('config.ini', encoding='utf-8')
            print("✅ 配置文件加载成功")

            # 加载字符替换规则
            self.load_replacement_rules()
            return True
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False

    def load_replacement_rules(self):
        """加载字符替换规则"""
        try:
            if self.config.has_section('REPLACEMENTS'):
                for field_name, rule in self.config.items('REPLACEMENTS'):
                    if ',' in rule:
                        old_char, new_char = rule.split(',', 1)
                        self.replacement_rules[field_name] = {
                            'old': old_char.strip(),
                            'new': new_char.strip()
                        }
                        print(f"✅ 加载替换规则: {field_name} -> '{old_char.strip()}' 替换为 '{new_char.strip()}'")

                if self.replacement_rules:
                    print(f"✅ 共加载 {len(self.replacement_rules)} 个字符替换规则")
                else:
                    print("ℹ️  未配置字符替换规则")
            else:
                print("ℹ️  配置文件中未找到 [REPLACEMENTS] 段")
        except Exception as e:
            print(f"⚠️  加载替换规则失败: {e}")
            self.replacement_rules = {}
    
    def verify_startup_password(self):
        """验证启动密码"""
        max_attempts = 3

        for attempt in range(max_attempts):
            try:
                password = getpass.getpass("请输入启动密码: ")
                if password == STARTUP_PASSWORD:
                    print("✅ 密码验证成功")
                    return True
                else:
                    remaining = max_attempts - attempt - 1
                    if remaining > 0:
                        print(f"❌ 密码错误，还有 {remaining} 次机会")
                    else:
                        print("❌ 密码错误次数过多，程序退出")
            except KeyboardInterrupt:
                print("\n程序被用户中断")
                return False

        return False
    
    def list_available_drivers(self):
        """列出可用的ODBC驱动程序"""
        try:
            available_drivers = pyodbc.drivers()
            print("系统中可用的ODBC驱动程序:")
            for i, driver in enumerate(available_drivers, 1):
                print(f"  {i}. {driver}")
            return available_drivers
        except Exception as e:
            print(f"无法获取驱动程序列表: {e}")
            return []

    def connect_database(self):
        """连接数据库"""
        try:
            host = self.config.get('DATABASE', 'host')
            port = self.config.get('DATABASE', 'port')
            database = self.config.get('DATABASE', 'database')
            auth_type = self.config.get('DATABASE', 'auth_type')

            print(f"正在连接数据库: {host}:{port}/{database}")

            # 获取系统可用驱动程序
            available_drivers = self.list_available_drivers()

            # 尝试多种ODBC驱动程序（按优先级排序）
            drivers = [
                "ODBC Driver 17 for SQL Server",
                "ODBC Driver 13 for SQL Server",
                "ODBC Driver 11 for SQL Server",
                "SQL Server Native Client 11.0",
                "SQL Server Native Client 10.0",
                "SQL Server"
            ]

            # 只尝试系统中实际存在的驱动程序
            existing_drivers = [d for d in drivers if d in available_drivers]
            if not existing_drivers:
                print("❌ 未找到兼容的SQL Server ODBC驱动程序")
                return False

            connection_string = None
            for driver in existing_drivers:
                try:
                    if auth_type.lower() == 'windows':
                        # Windows认证
                        connection_string = (
                            f"DRIVER={{{driver}}};"
                            f"SERVER={host},{port};"
                            f"DATABASE={database};"
                            f"Trusted_Connection=yes;"
                        )
                        print(f"尝试使用 Windows 认证方式，驱动: {driver}")
                    else:
                        # SQL认证
                        username = self.config.get('DATABASE', 'username')
                        password = self.config.get('DATABASE', 'password')
                        connection_string = (
                            f"DRIVER={{{driver}}};"
                            f"SERVER={host},{port};"
                            f"DATABASE={database};"
                            f"UID={username};"
                            f"PWD={password};"
                        )
                        print(f"尝试使用 SQL Server 认证方式，驱动: {driver}")

                    print(f"连接字符串: {connection_string}")
                    self.connection = pyodbc.connect(connection_string)
                    print(f"✅ 数据库连接成功，使用驱动: {driver}")
                    return True

                except pyodbc.Error as driver_error:
                    print(f"驱动 {driver} 连接失败: {driver_error}")
                    continue

            print("❌ 所有可用驱动程序都无法连接数据库")
            return False

        except Exception as e:
            print(f"❌ 连接过程中发生错误: {e}")
            return False
    
    def read_data(self):
        """读取数据库数据"""
        try:
            table_name = self.config.get('DATABASE', 'table_name')
            export_fields = self.config.get('DATABASE', 'export_fields').split(',')
            export_fields = [field.strip() for field in export_fields]
            
            # 构建SQL查询
            fields_str = ', '.join(export_fields)
            sql_query = f"SELECT {fields_str} FROM {table_name}"
            
            print(f"正在读取表: {table_name}")
            print(f"查询字段: {', '.join(export_fields)}")
            
            # 执行查询
            cursor = self.connection.cursor()
            cursor.execute(sql_query)
            
            # 获取数据
            rows = cursor.fetchall()
            
            # 转换为DataFrame
            self.data = pd.DataFrame([list(row) for row in rows], columns=export_fields)
            
            print(f"✅ 数据读取成功，共 {len(self.data)} 条记录")

            # 应用字符替换规则
            if self.replacement_rules:
                self.apply_replacements()

            return True
            
        except Exception as e:
            print(f"❌ 数据读取失败: {e}")
            return False

    def apply_replacements(self):
        """应用字符替换规则"""
        try:
            print("正在应用字符替换规则...")
            replacement_count = 0

            for field_name, rule in self.replacement_rules.items():
                if field_name in self.data.columns:
                    old_char = rule['old']
                    new_char = rule['new']

                    # 统计替换前的数据
                    before_count = self.data[field_name].astype(str).str.contains(old_char, regex=False).sum()

                    # 执行替换
                    self.data[field_name] = self.data[field_name].astype(str).str.replace(old_char, new_char, regex=False)

                    print(f"✅ 字段 '{field_name}': 替换了 {before_count} 条记录中的 '{old_char}' -> '{new_char}'")
                    replacement_count += before_count
                else:
                    print(f"⚠️  字段 '{field_name}' 不存在于数据中，跳过替换")

            if replacement_count > 0:
                print(f"✅ 字符替换完成，共处理 {replacement_count} 个字段值")
            else:
                print("ℹ️  未找到需要替换的数据")

        except Exception as e:
            print(f"❌ 字符替换失败: {e}")
    
    def verify_export_password(self):
        """验证导出密码"""
        max_attempts = 3

        print("\n准备导出数据，需要进行二次密码验证")

        for attempt in range(max_attempts):
            try:
                password = getpass.getpass("请输入导出密码: ")
                if password == EXPORT_PASSWORD:
                    print("✅ 导出密码验证成功")
                    return True
                else:
                    remaining = max_attempts - attempt - 1
                    if remaining > 0:
                        print(f"❌ 密码错误，还有 {remaining} 次机会")
                    else:
                        print("❌ 密码错误次数过多，取消导出")
            except KeyboardInterrupt:
                print("\n导出被用户中断")
                return False

        return False
    
    def export_to_excel(self):
        """导出数据到Excel"""
        try:
            output_filename = self.config.get('DATABASE', 'output_filename')
            
            # 添加时间戳到文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(output_filename)
            output_filename = f"{name}_{timestamp}{ext}"
            
            print(f"正在导出数据到: {output_filename}")
            
            # 导出到Excel
            self.data.to_excel(output_filename, index=False, engine='openpyxl')
            
            print(f"✅ 数据导出成功！")
            print(f"📁 文件位置: {os.path.abspath(output_filename)}")
            print(f"📊 导出记录数: {len(self.data)} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("数据库连接已关闭")
    
    def run(self):
        """主程序运行"""
        print("=" * 50)
        print("    数据库导出工具 v1.0")
        print("=" * 50)
        
        # 1. 加载配置
        if not self.load_config():
            return False
        
        # 2. 验证启动密码
        if not self.verify_startup_password():
            return False
        
        # 3. 连接数据库
        if not self.connect_database():
            return False
        
        # 4. 读取数据
        if not self.read_data():
            self.close_connection()
            return False
        
        # 5. 验证导出密码
        if not self.verify_export_password():
            self.close_connection()
            return False
        
        # 6. 导出数据
        success = self.export_to_excel()
        
        # 7. 关闭连接
        self.close_connection()
        
        return success

def main():
    """主函数"""
    tool = DatabaseExportTool()
    
    try:
        success = tool.run()
        if success:
            print("\n🎉 程序执行完成！")
        else:
            print("\n❌ 程序执行失败！")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
