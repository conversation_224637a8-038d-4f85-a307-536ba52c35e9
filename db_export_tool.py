#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库导出工具
支持SQL Server数据库连接和数据导出到Excel
"""

import sys
import os
import configparser
import getpass
import pyodbc
import pandas as pd
from datetime import datetime

class DatabaseExportTool:
    def __init__(self):
        self.config = None
        self.connection = None
        self.data = None
        
    def load_config(self):
        """加载配置文件"""
        try:
            self.config = configparser.ConfigParser()
            if not os.path.exists('config.ini'):
                print("❌ 配置文件 config.ini 不存在！")
                return False
            
            self.config.read('config.ini', encoding='utf-8')
            print("✅ 配置文件加载成功")
            return True
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def verify_startup_password(self):
        """验证启动密码"""
        startup_password = self.config.get('SECURITY', 'startup_password')
        max_attempts = 3
        
        for attempt in range(max_attempts):
            try:
                password = getpass.getpass("请输入启动密码: ")
                if password == startup_password:
                    print("✅ 密码验证成功")
                    return True
                else:
                    remaining = max_attempts - attempt - 1
                    if remaining > 0:
                        print(f"❌ 密码错误，还有 {remaining} 次机会")
                    else:
                        print("❌ 密码错误次数过多，程序退出")
            except KeyboardInterrupt:
                print("\n程序被用户中断")
                return False
        
        return False
    
    def connect_database(self):
        """连接数据库"""
        try:
            host = self.config.get('DATABASE', 'host')
            port = self.config.get('DATABASE', 'port')
            database = self.config.get('DATABASE', 'database')
            auth_type = self.config.get('DATABASE', 'auth_type')
            
            print(f"正在连接数据库: {host}:{port}/{database}")
            
            if auth_type.lower() == 'windows':
                # Windows认证
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={host},{port};"
                    f"DATABASE={database};"
                    f"Trusted_Connection=yes;"
                )
                print("使用 Windows 认证方式")
            else:
                # SQL认证
                username = self.config.get('DATABASE', 'username')
                password = self.config.get('DATABASE', 'password')
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={host},{port};"
                    f"DATABASE={database};"
                    f"UID={username};"
                    f"PWD={password};"
                )
                print("使用 SQL Server 认证方式")
            
            self.connection = pyodbc.connect(connection_string)
            print("✅ 数据库连接成功")
            return True
            
        except pyodbc.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 连接过程中发生错误: {e}")
            return False
    
    def read_data(self):
        """读取数据库数据"""
        try:
            table_name = self.config.get('DATABASE', 'table_name')
            export_fields = self.config.get('DATABASE', 'export_fields').split(',')
            export_fields = [field.strip() for field in export_fields]
            
            # 构建SQL查询
            fields_str = ', '.join(export_fields)
            sql_query = f"SELECT {fields_str} FROM {table_name}"
            
            print(f"正在读取表: {table_name}")
            print(f"查询字段: {', '.join(export_fields)}")
            
            # 执行查询
            cursor = self.connection.cursor()
            cursor.execute(sql_query)
            
            # 获取数据
            rows = cursor.fetchall()
            
            # 转换为DataFrame
            self.data = pd.DataFrame([list(row) for row in rows], columns=export_fields)
            
            print(f"✅ 数据读取成功，共 {len(self.data)} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 数据读取失败: {e}")
            return False
    
    def verify_export_password(self):
        """验证导出密码"""
        export_password = self.config.get('SECURITY', 'export_password')
        max_attempts = 3
        
        print("\n准备导出数据，需要进行二次密码验证")
        
        for attempt in range(max_attempts):
            try:
                password = getpass.getpass("请输入导出密码: ")
                if password == export_password:
                    print("✅ 导出密码验证成功")
                    return True
                else:
                    remaining = max_attempts - attempt - 1
                    if remaining > 0:
                        print(f"❌ 密码错误，还有 {remaining} 次机会")
                    else:
                        print("❌ 密码错误次数过多，取消导出")
            except KeyboardInterrupt:
                print("\n导出被用户中断")
                return False
        
        return False
    
    def export_to_excel(self):
        """导出数据到Excel"""
        try:
            output_filename = self.config.get('DATABASE', 'output_filename')
            
            # 添加时间戳到文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(output_filename)
            output_filename = f"{name}_{timestamp}{ext}"
            
            print(f"正在导出数据到: {output_filename}")
            
            # 导出到Excel
            self.data.to_excel(output_filename, index=False, engine='openpyxl')
            
            print(f"✅ 数据导出成功！")
            print(f"📁 文件位置: {os.path.abspath(output_filename)}")
            print(f"📊 导出记录数: {len(self.data)} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("数据库连接已关闭")
    
    def run(self):
        """主程序运行"""
        print("=" * 50)
        print("    数据库导出工具 v1.0")
        print("=" * 50)
        
        # 1. 加载配置
        if not self.load_config():
            return False
        
        # 2. 验证启动密码
        if not self.verify_startup_password():
            return False
        
        # 3. 连接数据库
        if not self.connect_database():
            return False
        
        # 4. 读取数据
        if not self.read_data():
            self.close_connection()
            return False
        
        # 5. 验证导出密码
        if not self.verify_export_password():
            self.close_connection()
            return False
        
        # 6. 导出数据
        success = self.export_to_excel()
        
        # 7. 关闭连接
        self.close_connection()
        
        return success

def main():
    """主函数"""
    tool = DatabaseExportTool()
    
    try:
        success = tool.run()
        if success:
            print("\n🎉 程序执行完成！")
        else:
            print("\n❌ 程序执行失败！")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
