#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试工具
用于诊断数据库连接问题
"""

import pyodbc
import configparser

def test_drivers():
    """测试可用的ODBC驱动程序"""
    print("=" * 50)
    print("系统中可用的ODBC驱动程序:")
    print("=" * 50)
    
    try:
        drivers = pyodbc.drivers()
        for i, driver in enumerate(drivers, 1):
            print(f"{i:2d}. {driver}")
        
        if not drivers:
            print("未找到任何ODBC驱动程序")
        
        return drivers
    except Exception as e:
        print(f"获取驱动程序列表失败: {e}")
        return []

def test_connection():
    """测试数据库连接"""
    print("\n" + "=" * 50)
    print("测试数据库连接")
    print("=" * 50)
    
    # 读取配置
    config = configparser.ConfigParser()
    config.read('config.ini', encoding='utf-8')
    
    host = config.get('DATABASE', 'host')
    port = config.get('DATABASE', 'port')
    database = config.get('DATABASE', 'database')
    username = config.get('DATABASE', 'username')
    password = config.get('DATABASE', 'password')
    
    print(f"连接参数:")
    print(f"  服务器: {host}:{port}")
    print(f"  数据库: {database}")
    print(f"  用户名: {username}")
    print(f"  密码: {'*' * len(password)}")
    
    # 获取可用驱动程序
    available_drivers = pyodbc.drivers()
    sql_drivers = [d for d in available_drivers if 'SQL Server' in d]
    
    if not sql_drivers:
        print("\n❌ 未找到SQL Server相关的ODBC驱动程序")
        return False
    
    print(f"\n找到 {len(sql_drivers)} 个SQL Server驱动程序:")
    for driver in sql_drivers:
        print(f"  - {driver}")
    
    # 尝试连接
    for driver in sql_drivers:
        print(f"\n尝试使用驱动: {driver}")
        
        connection_string = (
            f"DRIVER={{{driver}}};"
            f"SERVER={host},{port};"
            f"DATABASE={database};"
            f"UID={username};"
            f"PWD={password};"
        )
        
        print(f"连接字符串: {connection_string}")
        
        try:
            conn = pyodbc.connect(connection_string, timeout=10)
            print(f"✅ 连接成功！使用驱动: {driver}")
            
            # 测试查询
            cursor = conn.cursor()
            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0]
            print(f"数据库版本: {version}")
            
            conn.close()
            return True
            
        except pyodbc.Error as e:
            print(f"❌ 连接失败: {e}")
            continue
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            continue
    
    print("\n❌ 所有驱动程序都无法连接数据库")
    return False

def main():
    """主函数"""
    print("数据库连接诊断工具")
    
    # 测试驱动程序
    drivers = test_drivers()
    
    if not drivers:
        print("\n请安装SQL Server ODBC驱动程序")
        input("按回车键退出...")
        return
    
    # 测试连接
    success = test_connection()
    
    if success:
        print("\n🎉 数据库连接测试成功！")
    else:
        print("\n❌ 数据库连接测试失败！")
        print("\n可能的解决方案:")
        print("1. 检查数据库服务器是否运行")
        print("2. 检查网络连接和防火墙设置")
        print("3. 验证用户名和密码是否正确")
        print("4. 确认数据库名称是否正确")
        print("5. 安装或更新SQL Server ODBC驱动程序")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
