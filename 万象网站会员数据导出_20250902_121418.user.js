// ==UserScript==
// @name         万象网站会员数据导出
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  从万象网站导出会员数据到Excel和JSON文件
// <AUTHOR>
// @match        https://qian.sicent.com/*
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
// ==/UserScript==

(function() {
    'use strict';

    // 全局变量
    let isExporting = false;
    let exportData = [];
    let currentPage = 1;
    let totalPages = 1;
    let startPage = 1;
    let endPage = 1;
    let processedCount = 0;

    // 创建导出按钮和进度显示
    function createExportUI() {
        // 检查是否已经创建了UI
        if (document.getElementById('member-export-container')) {
            return;
        }

        const container = document.createElement('div');
        container.id = 'member-export-container';
        container.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #409EFF;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            min-width: 300px;
            font-family: Arial, sans-serif;
        `;

        container.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #409EFF;">会员数据导出工具</div>
            <div id="page-info" style="margin-bottom: 10px; font-size: 12px; color: #666;"></div>
            <div style="margin-bottom: 10px;">
                <label>开始页: <input type="number" id="start-page" value="1" min="1" style="width: 60px; margin-left: 5px;"></label>
                <label style="margin-left: 10px;">结束页: <input type="number" id="end-page" value="1" min="1" style="width: 60px; margin-left: 5px;"></label>
            </div>
            <button id="export-btn" style="
                background: #409EFF;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                width: 100%;
                margin-bottom: 10px;
            ">导出会员列表</button>
            <div id="progress-info" style="display: none;">
                <div style="margin-bottom: 5px; font-size: 12px;">
                    <span>进度: </span>
                    <span id="current-progress">0</span>/<span id="total-progress">0</span>
                </div>
                <div style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
                    <div id="progress-bar" style="background: #409EFF; height: 100%; width: 0%; transition: width 0.3s;"></div>
                </div>
                <div style="margin-top: 5px; font-size: 12px;">
                    已获取会员: <span id="member-count">0</span> 人
                </div>
            </div>
        `;

        document.body.appendChild(container);

        // 绑定事件
        document.getElementById('export-btn').addEventListener('click', startExport);
        
        // 更新页面信息
        updatePageInfo();
    }

    // 更新页面信息
    function updatePageInfo() {
        const pageInfo = getPageInfo();
        const pageInfoElement = document.getElementById('page-info');
        if (pageInfoElement && pageInfo) {
            pageInfoElement.textContent = `当前页: ${pageInfo.current}, 总页数: ${pageInfo.total}`;
            document.getElementById('end-page').value = pageInfo.total;
        }
    }

    // 获取页面信息
    function getPageInfo() {
        try {
            // 查找分页器
            const pager = document.querySelector('.el-pager');
            if (!pager) return null;

            // 获取当前页
            const activePage = pager.querySelector('.number.active');
            const current = activePage ? parseInt(activePage.textContent) : 1;

            // 获取总页数 - 查找最后一个数字
            const numbers = pager.querySelectorAll('.number');
            let total = 1;
            numbers.forEach(num => {
                const pageNum = parseInt(num.textContent);
                if (!isNaN(pageNum) && pageNum > total) {
                    total = pageNum;
                }
            });

            return { current, total };
        } catch (error) {
            console.error('获取页面信息失败:', error);
            return null;
        }
    }

    // 开始导出
    async function startExport() {
        if (isExporting) return;

        const startPageInput = document.getElementById('start-page');
        const endPageInput = document.getElementById('end-page');
        
        startPage = parseInt(startPageInput.value);
        endPage = parseInt(endPageInput.value);

        if (startPage > endPage) {
            alert('开始页不能大于结束页！');
            return;
        }

        isExporting = true;
        exportData = [];
        processedCount = 0;

        // 显示进度
        const progressInfo = document.getElementById('progress-info');
        const exportBtn = document.getElementById('export-btn');
        progressInfo.style.display = 'block';
        exportBtn.textContent = '导出中...';
        exportBtn.disabled = true;

        document.getElementById('total-progress').textContent = endPage - startPage + 1;

        try {
            // 如果当前页不是开始页，先跳转到开始页
            const pageInfo = getPageInfo();
            if (pageInfo && pageInfo.current !== startPage) {
                await navigateToPage(startPage);
                await sleep(2000); // 等待页面加载
            }

            // 开始逐页提取数据
            for (let page = startPage; page <= endPage; page++) {
                console.log(`正在处理第 ${page} 页...`);
                
                // 提取当前页数据
                const pageData = extractMemberData();
                exportData.push(...pageData);
                
                // 更新进度
                processedCount = page - startPage + 1;
                updateProgress();

                // 如果不是最后一页，跳转到下一页
                if (page < endPage) {
                    const success = await navigateToNextPage();
                    if (!success) {
                        alert(`跳转到第 ${page + 1} 页失败，导出中断`);
                        break;
                    }
                    await sleep(2000); // 等待页面加载
                }
            }

            // 导出文件
            if (exportData.length > 0) {
                exportToFiles();
                alert(`导出完成！共导出 ${exportData.length} 条会员数据`);
            } else {
                alert('未找到会员数据！');
            }

        } catch (error) {
            console.error('导出过程中出错:', error);
            alert('导出过程中出错: ' + error.message);
        } finally {
            // 重置状态
            isExporting = false;
            progressInfo.style.display = 'none';
            exportBtn.textContent = '导出会员列表';
            exportBtn.disabled = false;
        }
    }

    // 跳转到指定页面
    async function navigateToPage(targetPage) {
        return new Promise((resolve) => {
            // 这里可以实现直接跳转到指定页面的逻辑
            // 由于示例中没有直接跳转的按钮，我们使用循环点击的方式
            const pageInfo = getPageInfo();
            if (!pageInfo) {
                resolve(false);
                return;
            }

            if (pageInfo.current === targetPage) {
                resolve(true);
                return;
            }

            // 简化处理：如果目标页面距离当前页面太远，提示用户手动跳转
            if (Math.abs(targetPage - pageInfo.current) > 10) {
                alert(`请手动跳转到第 ${targetPage} 页，然后重新点击导出按钮`);
                resolve(false);
                return;
            }

            // 通过点击下一页或上一页按钮来跳转
            const navigateStep = () => {
                const currentPageInfo = getPageInfo();
                if (!currentPageInfo || currentPageInfo.current === targetPage) {
                    resolve(true);
                    return;
                }

                if (currentPageInfo.current < targetPage) {
                    const nextBtn = document.querySelector('.btn-next:not([disabled])');
                    if (nextBtn) {
                        nextBtn.click();
                        setTimeout(navigateStep, 1500);
                    } else {
                        resolve(false);
                    }
                } else {
                    const prevBtn = document.querySelector('.btn-prev:not([disabled])');
                    if (prevBtn) {
                        prevBtn.click();
                        setTimeout(navigateStep, 1500);
                    } else {
                        resolve(false);
                    }
                }
            };

            navigateStep();
        });
    }

    // 跳转到下一页
    async function navigateToNextPage() {
        return new Promise((resolve) => {
            const nextBtn = document.querySelector('.btn-next:not([disabled])');
            if (nextBtn) {
                nextBtn.click();
                // 等待页面加载完成
                setTimeout(() => {
                    resolve(true);
                }, 1500);
            } else {
                resolve(false);
            }
        });
    }

    // 提取会员数据
    function extractMemberData() {
        const members = [];
        const rows = document.querySelectorAll('.el-table__row');

        rows.forEach(row => {
            try {
                const cells = row.querySelectorAll('.el-table__cell .cell');
                if (cells.length >= 8) {
                    const member = {
                        网吧账户: cells[0].textContent.trim(),
                        卡号: cells[1].textContent.trim(),
                        证件号: cells[2].textContent.trim(),
                        会员姓名: cells[3].textContent.trim(),
                        会员等级: cells[4].textContent.trim(),
                        状态: cells[5].textContent.trim(),
                        等级积分: cells[6].textContent.trim(),
                        可用积分: cells[7].textContent.trim(),
                        注册时间: cells[8] ? cells[8].textContent.trim() : '',
                        最后登录: cells[9] ? cells[9].textContent.trim() : ''
                    };
                    members.push(member);
                }
            } catch (error) {
                console.error('提取会员数据出错:', error);
            }
        });

        return members;
    }

    // 更新进度
    function updateProgress() {
        const currentProgress = document.getElementById('current-progress');
        const progressBar = document.getElementById('progress-bar');
        const memberCount = document.getElementById('member-count');

        if (currentProgress) currentProgress.textContent = processedCount;
        if (memberCount) memberCount.textContent = exportData.length;
        
        const totalProgress = parseInt(document.getElementById('total-progress').textContent);
        const percentage = (processedCount / totalProgress) * 100;
        if (progressBar) progressBar.style.width = percentage + '%';
    }

    // 导出到文件
    function exportToFiles() {
        // 导出Excel
        exportToExcel();
        // 导出JSON
        exportToJSON();
    }

    // 导出Excel文件
    function exportToExcel() {
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "会员数据");
        
        const fileName = `会员数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
        XLSX.writeFile(wb, fileName);
    }

    // 导出JSON文件
    function exportToJSON() {
        const jsonData = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `会员数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 工具函数：延时
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 页面加载完成后创建UI
    function init() {
        // 检查是否在会员列表页面
        if (window.location.href.includes('#/customer/member/list')) {
            // 等待页面完全加载
            setTimeout(() => {
                createExportUI();
            }, 2000);
        }
    }

    // 监听URL变化
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            if (url.includes('#/customer/member/list')) {
                setTimeout(() => {
                    createExportUI();
                }, 2000);
            } else {
                // 移除UI
                const container = document.getElementById('member-export-container');
                if (container) {
                    container.remove();
                }
            }
        }
    }).observe(document, { subtree: true, childList: true });

    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
