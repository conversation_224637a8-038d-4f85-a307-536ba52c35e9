@echo off
echo CSV-JSON数据处理工具打包脚本
echo ============================

echo 步骤1: 清理旧文件
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo.
echo 步骤2: 检查Python环境
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 步骤3: 安装/更新PyInstaller
pip install --upgrade pyinstaller

echo.
echo 步骤4: 打包程序
pyinstaller --onefile --console --name "CSV数据处理工具" csv_json_processor.py

if exist "dist\CSV数据处理工具.exe" (
    echo.
    echo ✅ 打包成功！
    echo 📁 EXE文件: dist\CSV数据处理工具.exe
    
    echo.
    echo 步骤5: 复制配置文件到dist目录
    copy "processor_config.ini" "dist\config.ini"
    
    echo.
    echo 🎉 打包完成！
    echo 📋 使用说明:
    echo   1. 将EXE文件和config.ini放在同一目录
    echo   2. 将要处理的CSV和JSON文件放在同一目录
    echo   3. 运行EXE文件，按提示选择文件
    echo.
    echo 📁 完整文件列表:
    echo   - CSV数据处理工具.exe  (主程序)
    echo   - config.ini           (配置文件)
    echo   - 你的CSV文件          (数据源)
    echo   - 你的JSON文件         (数据源)
    
) else (
    echo ❌ 打包失败，请检查错误信息
)

echo.
pause
