@echo off
echo ===================================================
echo           数据库导出工具打包程序
echo ===================================================
echo.
echo 此批处理文件将使用 PyInstaller 打包数据库导出工具
echo 打包过程需要几分钟时间，请耐心等待...
echo.
echo 按任意键开始打包...
pause > nul

echo.
echo 正在启动打包脚本...
python build_exe.py

echo.
if %ERRORLEVEL% EQU 0 (
    echo 打包成功完成！
) else (
    echo 打包过程中出现错误，请查看上方日志。
)

echo.
echo 按任意键退出...
pause > nul