#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import configparser
import os

# 测试配置文件
print("测试配置文件...")

config = configparser.ConfigParser()

# 检查processor_config.ini
if os.path.exists('processor_config.ini'):
    print("✅ 找到 processor_config.ini")
    config.read('processor_config.ini', encoding='utf-8')
    
    if config.has_section('SETTINGS'):
        print("✅ 找到 SETTINGS 段")
        print(f"store_account: {config.get('SETTINGS', 'store_account')}")
        print(f"grade_source: {config.get('SETTINGS', 'grade_source')}")
        print(f"match_field: {config.get('SETTINGS', 'match_field')}")
    else:
        print("❌ 未找到 SETTINGS 段")
else:
    print("❌ 未找到 processor_config.ini")

print("\n配置文件测试完成")
