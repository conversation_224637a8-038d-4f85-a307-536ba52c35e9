@echo off
echo 最终打包解决方案
echo ================

echo 步骤1: 清理环境
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo.
echo 步骤2: 安装/更新依赖
pip install --upgrade pyinstaller
pip install --upgrade pyodbc

echo.
echo 步骤3: 使用最基本的打包命令
pyinstaller --onefile --console db_export_simple.py

echo.
echo 步骤4: 重命名和复制文件
if exist "dist\db_export_simple.exe" (
    ren "dist\db_export_simple.exe" "数据库导出工具.exe"
    copy "config.ini" "dist\config.ini"
    echo ✅ 打包成功！
    echo 文件位置: dist\数据库导出工具.exe
) else (
    echo ❌ 打包失败，EXE文件未生成
)

echo.
pause
