# CSV-JSON数据处理工具 - 完整解决方案

## 🎯 程序功能
这是一个Python程序，用于：
1. 读取CSV文件中的会员数据
2. 从JSON文件中匹配对应的详细信息
3. 生成新格式的CSV文件

## 📁 文件清单

### 核心文件
- `csv_json_processor.py` - 主程序（**最重要**）
- `processor_config.ini` - 配置文件
- `使用说明_CSV处理工具.md` - 详细使用说明

### 示例文件
- `sample_data.csv` - 示例输入CSV文件
- `sample_users.json` - 示例JSON数据文件

### 打包文件
- `build_processor.bat` - 打包脚本
- `simple_pack.bat` - 简化打包脚本

## 🚀 立即使用方法

### 方法1: 直接运行Python程序（推荐）
```bash
# 1. 确保有Python环境
python --version

# 2. 将你的CSV和JSON文件放在程序同一目录

# 3. 运行程序
python csv_json_processor.py

# 4. 按提示选择文件即可
```

### 方法2: 打包为EXE文件
```bash
# 1. 安装PyInstaller
pip install pyinstaller

# 2. 打包
pyinstaller --onefile --console csv_json_processor.py

# 3. 复制配置文件
copy processor_config.ini dist\config.ini

# 4. 运行
dist\csv_json_processor.exe
```

## 📋 输入文件格式要求

### CSV文件格式（必须包含这4列）
```csv
snameshow,scardidshow,sDis,sopenid
张*三,*123456,普通会员,abc123def456
李*四,*789012,黄金会员,def789ghi012
```

### JSON文件格式（必须包含all_users数组）
```json
{
  "all_users": [
    {
      "openid": "abc123def456",
      "grade_name": "钻石会员",
      "grow_points": "500",
      "curr_points": "1200",
      "curr_wallet": 150.75,
      "curr_barMoney": 88.50
    }
  ]
}
```

## 📊 输出文件格式

生成的CSV文件包含8列：
| 列名 | 数据来源 | 说明 |
|------|----------|------|
| 门店账户 | 配置文件 | 固定值，如：dgdxfwk |
| 姓名 | CSV的snameshow | 会员姓名 |
| 证件号码 | CSV的scardidshow | 证件号码 |
| 会员等级 | 配置决定 | CSV的sDis 或 JSON的grade_name |
| 等级积分 | JSON的grow_points | 整数格式 |
| 可用积分 | JSON的curr_points | 整数格式 |
| 本金 | JSON的curr_wallet | 保留2位小数 |
| 赠费 | JSON的curr_barMoney | 保留2位小数 |

## ⚙️ 配置文件说明

`config.ini` 文件内容：
```ini
[SETTINGS]
# 门店账户（所有记录的固定值）
store_account = dgdxfwk

# 会员等级来源: csv 或 json
grade_source = json

# 默认会员等级
default_grade = 普通会员
```

## 🔄 程序运行流程

1. **启动程序** → 显示欢迎信息
2. **加载配置** → 读取config.ini文件
3. **选择CSV文件** → 列出当前目录的CSV文件供选择
4. **选择JSON文件** → 列出当前目录的JSON文件供选择
5. **读取数据** → 解析CSV和JSON文件
6. **匹配处理** → 根据openid匹配数据
7. **生成输出** → 创建新的CSV文件

## 📝 使用示例

### 运行过程示例
```
CSV和JSON数据处理工具 v1.0
============================================================
✅ 配置文件加载成功

📁 当前目录下的 CSV 文件:
  1. sample_data.csv
  2. member_data.csv

请选择 CSV 文件 (输入序号): 1
✅ 已选择: sample_data.csv

📁 当前目录下的 JSON 文件:
  1. sample_users.json
  2. user_data.json

请选择 JSON 文件 (输入序号): 1
✅ 已选择: sample_users.json

✅ CSV文件读取成功，共 3 条记录
✅ JSON文件读取成功，共 3 个用户记录
🔄 正在处理数据...
  ✅ 第1行: 张*三 - 找到匹配数据
  ✅ 第2行: 李*四 - 找到匹配数据
  ⚠️  第3行: 王*五 - 未找到匹配数据，使用默认值
✅ 数据处理完成，共处理 3 条记录
✅ 输出文件生成成功: dgdxfwk_20241202_143025.csv
📊 输出记录数: 3 条

🎉 程序执行完成！
```

## 🛠️ 故障排除

### 常见问题
1. **找不到文件** → 确保CSV和JSON文件在程序同一目录
2. **编码错误** → 确保文件使用UTF-8编码
3. **JSON格式错误** → 检查JSON文件格式是否正确
4. **没有匹配数据** → 检查openid字段是否一致

### 调试方法
- 程序会显示详细的处理过程
- 检查生成的config.ini文件
- 使用示例文件测试程序功能

## 🎉 快速开始

1. **下载文件**：确保有 `csv_json_processor.py`
2. **准备数据**：将你的CSV和JSON文件放在同一目录
3. **运行程序**：`python csv_json_processor.py`
4. **按提示操作**：选择文件，等待处理完成
5. **查看结果**：检查生成的新CSV文件

## 📞 技术特点

- ✅ 只使用Python标准库（csv, json, configparser等）
- ✅ 自动创建配置文件
- ✅ 友好的用户界面
- ✅ 详细的处理日志
- ✅ 错误处理和默认值
- ✅ 可打包为独立EXE文件
- ✅ 支持中文文件名和内容

这个程序已经完全满足您的需求，可以直接使用！
