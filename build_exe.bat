@echo off
echo 正在清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "数据库导出工具.spec" del "数据库导出工具.spec"

echo.
echo 正在打包程序为EXE文件...
pyinstaller --onefile --console --name "数据库导出工具" db_export_tool.py

echo.
echo 正在复制配置文件...
copy "config.ini" "dist\config.ini"

echo.
echo 打包完成！
echo EXE文件位于 dist 目录中
echo config.ini 已自动复制到 dist 目录
echo.
echo 测试运行方法:
echo   cd dist
echo   .\数据库导出工具.exe
echo.
pause
