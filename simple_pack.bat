@echo off
echo 简单打包CSV处理工具
echo ===================

echo 清理旧文件...
if exist "csv_processor_dist" rmdir /s /q "csv_processor_dist"
if exist "csv_processor_build" rmdir /s /q "csv_processor_build"
if exist "CSV数据处理工具.spec" del "CSV数据处理工具.spec"

echo.
echo 开始打包...
pyinstaller --onefile --console --distpath csv_processor_dist --workpath csv_processor_build csv_json_processor.py

if exist "csv_processor_dist\csv_json_processor.exe" (
    echo ✅ 打包成功！
    copy "processor_config.ini" "csv_processor_dist\config.ini"
    copy "sample_data.csv" "csv_processor_dist\"
    copy "sample_users.json" "csv_processor_dist\"
    echo 文件已复制到 csv_processor_dist 目录
) else (
    echo ❌ 打包失败
)

pause
