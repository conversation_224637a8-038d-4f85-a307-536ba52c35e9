@echo off
echo 使用spec文件打包程序
echo =====================

echo 正在清理旧的打包文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo 检查依赖库是否已安装...
python -c "import pyodbc, pandas, openpyxl; print('✅ 所有依赖库已安装')" 2>nul
if errorlevel 1 (
    echo ❌ 缺少必要的依赖库
    echo 正在安装依赖库...
    pip install pyodbc pandas openpyxl pyinstaller
    if errorlevel 1 (
        echo 依赖库安装失败，请手动安装
        pause
        exit /b 1
    )
)

echo.
echo 正在使用spec文件打包...
pyinstaller "数据库导出工具.spec"

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 正在复制配置文件...
copy "config.ini" "dist\config.ini"

echo.
echo ✅ 打包完成！
echo 📁 EXE文件位于 dist 目录中
echo 📄 config.ini 已自动复制到 dist 目录
echo.
echo 🚀 测试运行方法:
echo   cd dist
echo   .\数据库导出工具.exe
echo.
echo 或者直接运行: test_exe.bat
echo.
pause
