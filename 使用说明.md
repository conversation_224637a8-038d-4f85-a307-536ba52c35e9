# 数据库导出工具使用说明

## 功能概述
这是一个通用的SQL Server数据库导出工具，支持：
- 连接本地或远程SQL Server数据库
- 支持SQL认证和Windows认证
- 导出指定表的指定字段到Excel文件
- 字符替换功能
- 双重密码保护

## 文件说明

### 主要文件
- `db_export_tool.py` - 主程序
- `config.ini` - 配置文件
- `test_connection.py` - 连接测试工具

### 辅助文件
- `requirements.txt` - Python依赖库列表
- `install_dependencies.bat` - 安装依赖脚本
- `build_exe.bat` - 打包EXE脚本

## 配置说明

### 数据库配置 [DATABASE]
```ini
host = *************        # 数据库服务器地址
port = 1433                 # 端口号
database = WXOL4            # 数据库名称
auth_type = sql             # 认证方式: sql 或 windows
username = sa               # SQL认证用户名
password = your_password    # SQL认证密码
table_name = dbo.tUsers     # 要导出的表名
export_fields = sCardID,sIDCard,sDis,scardidshow,sidcardshow,snameshow,sopenid  # 导出字段
output_filename = exported_data.xlsx  # 输出文件名
```

### 字符替换配置 [REPLACEMENTS]
```ini
# 格式: 字段名 = 原字符,替换字符
scardidshow = *,************    # 将单个*替换为12个*
sidcardshow = *,****           # 将单个*替换为4个*
snameshow = 张,王              # 将"张"替换为"王"
```

## 密码配置
程序中有两个密码，直接在代码中配置：

在 `db_export_tool.py` 文件开头：
```python
# 程序启动密码
STARTUP_PASSWORD = "qq616887"

# 导出确认密码  
EXPORT_PASSWORD = "9sheng"
```

## 使用步骤

### 1. 安装依赖
```bash
# 方法1: 使用批处理文件
install_dependencies.bat

# 方法2: 手动安装
pip install -r requirements.txt
```

### 2. 配置数据库连接
编辑 `config.ini` 文件，设置正确的数据库连接参数。

### 3. 测试连接（推荐）
```bash
python test_connection.py
```
这个工具会：
- 显示系统中可用的ODBC驱动程序
- 测试数据库连接
- 提供详细的诊断信息

### 4. 运行主程序
```bash
python db_export_tool.py
```

程序运行流程：
1. 输入启动密码（qq616887）
2. 连接数据库
3. 读取数据并显示记录数
4. 输入导出密码（9sheng）
5. 应用字符替换规则
6. 导出到Excel文件

### 5. 打包为EXE（可选）
```bash
build_exe.bat
```
生成的EXE文件在 `dist` 目录中，需要将 `config.ini` 复制到EXE同一目录。

## 字符替换功能详解

### 配置格式
```ini
[REPLACEMENTS]
字段名 = 原字符,替换字符
```

### 示例场景
假设数据库中 `scardidshow` 字段的值为 `*252829`，配置：
```ini
scardidshow = *,************
```
替换后的结果为：`************252829`

### 多字段替换
```ini
scardidshow = *,************
sidcardshow = *,****
snameshow = 测试,保密
```

### 注意事项
- 替换是精确匹配，区分大小写
- 支持任意字符的替换
- 如果字段不存在，会跳过该替换规则
- 替换会在数据读取后、导出前执行

## 常见问题

### 1. 数据库连接失败
- 检查数据库服务是否运行
- 验证IP地址、端口、用户名、密码
- 确认防火墙设置
- 运行 `test_connection.py` 进行诊断

### 2. ODBC驱动程序问题
程序会自动尝试多种驱动程序：
- ODBC Driver 17 for SQL Server
- ODBC Driver 13 for SQL Server
- SQL Server Native Client 11.0
- 等等

如果都不可用，请安装SQL Server ODBC驱动程序。

### 3. 字段不存在
检查 `export_fields` 配置中的字段名是否正确，确保与数据库表结构一致。

### 4. 权限问题
确保数据库用户有读取指定表的权限。

## 安全说明
- 程序包含双重密码保护
- 密码直接写在代码中，不存储在配置文件
- 建议定期更改密码
- 生成EXE后，源代码中的密码仍然可见，注意保护

## 技术支持
如遇问题，请提供：
1. 错误信息截图
2. `test_connection.py` 的输出结果
3. 数据库版本和配置信息
