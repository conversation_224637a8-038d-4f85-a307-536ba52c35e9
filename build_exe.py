#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将 db_export_simple.py 打包成独立的 EXE 文件
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

def print_header(text):
    """打印带格式的标题"""
    print("\n" + "=" * 60)
    print(f"  {text}")
    print("=" * 60)

def print_step(step_num, text):
    """打印步骤信息"""
    print(f"\n[步骤 {step_num}] {text}")
    print("-" * 60)

def run_command(command):
    """运行命令并返回结果"""
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(f"错误: {result.stderr}")
    return result.returncode == 0

def main():
    """主函数"""
    print_header("数据库导出工具打包脚本")
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"当前工作目录: {current_dir}")
    
    # 步骤1: 检查环境
    print_step(1, "检查环境")
    
    # 检查 Python 版本
    python_version = sys.version.split()[0]
    print(f"Python 版本: {python_version}")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        pyinstaller_version = PyInstaller.__version__
        print(f"PyInstaller 版本: {pyinstaller_version}")
    except ImportError:
        print("错误: 未安装 PyInstaller")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查 pyodbc
    try:
        import pyodbc
        pyodbc_version = pyodbc.version
        print(f"pyodbc 版本: {pyodbc_version}")
    except ImportError:
        print("错误: 未安装 pyodbc")
        print("请运行: pip install pyodbc")
        return False
    
    # 检查源文件
    source_file = os.path.join(current_dir, "db_export_simple.py")
    if not os.path.exists(source_file):
        print(f"错误: 源文件不存在: {source_file}")
        return False
    print(f"源文件: {source_file} (已找到)")
    
    # 检查配置文件
    config_file = os.path.join(current_dir, "config.ini")
    if not os.path.exists(config_file):
        print(f"警告: 配置文件不存在: {config_file}")
        print("将创建一个示例配置文件")
    else:
        print(f"配置文件: {config_file} (已找到)")
    
    # 步骤2: 准备打包
    print_step(2, "准备打包")
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(current_dir, f"dist_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 步骤3: 执行打包
    print_step(3, "执行打包")
    
    # 构建 PyInstaller 命令
    pyinstaller_cmd = (
        f"python -m PyInstaller "
        f"--onefile "              # 单文件模式
        f"--console "              # 控制台应用
        f"--clean "                # 清理临时文件
        f"--name \"数据库导出工具\" "  # 输出文件名
        f"--add-data \"{config_file};.\" "  # 添加配置文件
        f"--hidden-import=pyodbc "  # 添加隐式导入
        f"--hidden-import=configparser "
        f"--hidden-import=csv "
        f"--hidden-import=datetime "
        f"\"{source_file}\""        # 源文件
    )
    
    # 执行打包命令
    success = run_command(pyinstaller_cmd)
    if not success:
        print("错误: PyInstaller 打包失败")
        return False
    
    # 步骤4: 整理输出文件
    print_step(4, "整理输出文件")
    
    # 复制 EXE 文件到输出目录
    dist_dir = os.path.join(current_dir, "dist")
    exe_file = os.path.join(dist_dir, "数据库导出工具.exe")
    if not os.path.exists(exe_file):
        print(f"错误: 打包后的 EXE 文件不存在: {exe_file}")
        return False
    
    # 复制 EXE 文件
    target_exe = os.path.join(output_dir, "数据库导出工具.exe")
    shutil.copy2(exe_file, target_exe)
    print(f"已复制 EXE 文件到: {target_exe}")
    
    # 复制配置文件
    target_config = os.path.join(output_dir, "config.ini")
    shutil.copy2(config_file, target_config)
    print(f"已复制配置文件到: {target_config}")
    
    # 创建说明文件
    readme_file = os.path.join(output_dir, "使用说明.txt")
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write("数据库导出工具使用说明\n")
        f.write("====================\n\n")
        f.write("1. 运行方式：双击 \"数据库导出工具.exe\" 即可启动程序\n")
        f.write("2. 配置文件：程序会自动读取同目录下的 config.ini 文件\n")
        f.write("3. 启动密码：请联系管理员获取\n")
        f.write("4. 导出密码：请联系管理员获取\n")
        f.write("5. 导出文件：程序会在同目录下生成 CSV 格式的导出文件\n\n")
        f.write(f"打包时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Python 版本：{python_version}\n")
        f.write(f"PyInstaller 版本：{pyinstaller_version}\n")
    print(f"已创建说明文件: {readme_file}")
    
    # 步骤5: 清理临时文件
    print_step(5, "清理临时文件")
    
    # 删除 build 目录
    build_dir = os.path.join(current_dir, "build")
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
        print(f"已删除临时目录: {build_dir}")
    
    # 删除 spec 文件
    spec_file = os.path.join(current_dir, "数据库导出工具.spec")
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print(f"已删除临时文件: {spec_file}")
    
    # 完成
    print_header("打包完成")
    print(f"打包后的文件位于: {output_dir}")
    print(f"EXE 文件: {target_exe}")
    print(f"配置文件: {target_config}")
    print(f"说明文件: {readme_file}")
    print("\n打包成功！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n打包失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n打包出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        input("\n按回车键退出...")