@echo off
echo 测试EXE文件运行
echo ================

if not exist "dist\数据库导出工具.exe" (
    echo 错误: 未找到 dist\数据库导出工具.exe
    echo 请先运行 build_exe.bat 进行打包
    pause
    exit /b 1
)

if not exist "dist\config.ini" (
    echo 错误: 未找到 dist\config.ini
    echo 正在复制配置文件...
    copy "config.ini" "dist\config.ini"
)

echo 切换到 dist 目录并运行程序...
cd dist
echo 当前目录: %CD%
echo 文件列表:
dir

echo.
echo 运行程序...
"数据库导出工具.exe"

echo.
echo 程序运行结束
pause
