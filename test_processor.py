#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV-JSON处理器
"""

import csv_json_processor

def test_config():
    """测试配置文件加载"""
    processor = csv_json_processor.CSVJSONProcessor()
    
    print("测试配置文件加载...")
    success = processor.load_config()
    
    if success:
        print("✅ 配置文件加载成功")
        print(f"门店账户: {processor.config.get('SETTINGS', 'store_account')}")
        print(f"等级来源: {processor.config.get('SETTINGS', 'grade_source')}")
        print(f"匹配字段: {processor.config.get('SETTINGS', 'match_field')}")
    else:
        print("❌ 配置文件加载失败")
    
    return success

def test_json_structure():
    """测试JSON文件结构"""
    import json
    
    json_files = ['merged_api_data_pages_1_to_265_20250902_152259.json']
    
    for json_file in json_files:
        try:
            print(f"\n测试JSON文件: {json_file}")
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'all_users' in data:
                users = data['all_users']
                print(f"✅ 找到all_users，包含 {len(users)} 个用户")
                
                if users:
                    first_user = users[0]
                    print("第一个用户的字段:")
                    for key in first_user.keys():
                        print(f"  - {key}: {first_user[key]}")
                    
                    # 检查匹配字段
                    if 'bind_from' in first_user:
                        print(f"✅ 找到bind_from字段: {first_user['bind_from']}")
                    else:
                        print("❌ 未找到bind_from字段")
            else:
                print("❌ 未找到all_users字段")
                
        except Exception as e:
            print(f"❌ 读取JSON文件失败: {e}")

if __name__ == "__main__":
    print("CSV-JSON处理器测试")
    print("=" * 40)
    
    # 测试配置
    config_ok = test_config()
    
    if config_ok:
        # 测试JSON结构
        test_json_structure()
    
    print("\n测试完成")
    input("按回车键退出...")
