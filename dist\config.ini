[DATABASE]
# 数据库连接配置
# host = localhost
host = *************
port = 1433
database = WXOL4

# 认证方式: sql 或 windows
auth_type = sql

# SQL认证用户名和密码 (当auth_type=sql时使用)
username = sa
password = Admin123!@#

# 数据表配置
table_name = dbo.tUsers

# 导出字段配置 (按顺序导出)
export_fields = snameshow,scardidshow,sDis,sopenid

# 导出文件配置
output_filename = exported_data.xlsx

# 字符替换配置
[REPLACEMENTS]
# 格式: 字段名 = 原字符,替换字符
# 支持多个字段配置，每行一个
# 示例: scardidshow = *,************
scardidshow = *,************
# sidcardshow = *,****
# 如果不需要替换某个字段，可以注释掉或删除对应行


