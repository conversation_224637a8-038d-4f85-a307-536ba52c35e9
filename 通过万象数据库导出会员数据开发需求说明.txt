
这是一个PYTHON程序，需要通过链接本机的 Microsoft SQL Server 2008 R2 数据库进行读取数据库中的数据。
链接本地MS SQL Server 数据库，通常有 Host:localhost  , Port:1433 , Database: WXOL4
认证方式：应该有两种，一种是SQL数据库密码， 另外一种是 Windows Authentication 
我希望两种方式都支持，并且程序通过conf 配置文件，来控制链接方式，和预先设置好SQL认证密码，和SQL的各项参数信息。

程序启动后，首先检测并连接SQL，提示是否连接成功。
然后开始读取数据库表 dbo.tUsers 中的数据，并显示出一共有多少条数据的信息，便于我们核对是否正确读取到内容，同时读取表名称，也需要能在conf中支持配置，因为我可能需要读取别的表的内容。
接下来是，导出 conf 中配置中的 dbo.tUsers.sCardID , dbo.tUsers.sIDCard , dbo.tUsers.sDis ,dbo.tUsers.scardidshow , dbo.tUsers.sidcardshow , dbo.tUsers.snameshow ,dbo.tUsers.sopenid  这些字段的每条数据内容。
导出后的数据使用excel 存放， excel列的顺序，根据我们配置中 导出列内容的顺序排列。
导出完整后，提示最终导出了多少条数据信息。


这会是一个通用的数据导出工具，只需要我正确配置链接数据库的参数，和需要导出的数据库字段，就能支持导出数据到EXCEL中。

这也是一个独立的PYTHON程序，我希望用比较少的支持库，最好是只用核心支持库就能实现。
窗口可以是控制台交付的方式来完成，最终我还需要生成为EXE文件独立运行。

在这个过程中，我希望在程序中，加2出密码验证，防止程序被滥用。
第一次密码验证，启动程序后就需要输入密码：qq616887   ,输入密码的时候，不会显示密文，回车后，校验正确后进行下一步操作。不正确的话提示密码错误重试。

第二次密码校验，在正确读取出数据库表的内容后，开始执行导出时，提示密码验证,  密码设置为:9sheng
验证正确后，才开始导出数据为EXCEL。



