@echo off
echo ===================================================
echo           数据库导出工具 - 验证程序
echo ===================================================
echo.
echo 此批处理文件将验证打包后的 EXE 文件是否正常工作
echo.

set EXE_PATH=dist\数据库导出工具.exe

if not exist %EXE_PATH% (
    echo 错误: 未找到打包后的 EXE 文件: %EXE_PATH%
    echo 请先运行打包程序生成 EXE 文件。
    goto end
)

echo 找到 EXE 文件: %EXE_PATH%
echo.
echo 正在验证 EXE 文件...
echo.
echo 将运行 EXE 文件并显示版本信息
echo 如果程序正常启动，则表示打包成功
echo.
echo 按任意键开始验证...
pause > nul

echo.
echo 正在启动程序...
echo.
%EXE_PATH%

:end
echo.
echo 验证完成。
echo.
echo 按任意键退出...
pause > nul