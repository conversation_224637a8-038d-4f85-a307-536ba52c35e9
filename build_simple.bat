@echo off
echo 打包简化版数据库导出工具
echo ========================

echo 正在清理旧文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo.
echo 检查pyodbc模块...
python -c "import pyodbc; print('✅ pyodbc 可用')" 2>nul
if errorlevel 1 (
    echo ❌ 缺少 pyodbc 模块
    echo 正在安装...
    pip install pyodbc
    if errorlevel 1 (
        echo 安装失败，请手动安装: pip install pyodbc
        pause
        exit /b 1
    )
)

echo.
echo 正在打包简化版程序...
pyinstaller --onefile --console --name "数据库导出工具_简化版" db_export_simple.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 复制配置文件...
copy "config.ini" "dist\config.ini"

echo.
echo ✅ 打包完成！
echo 📁 EXE文件: dist\数据库导出工具_简化版.exe
echo 📄 配置文件: dist\config.ini
echo.
echo 🚀 测试运行:
echo   cd dist
echo   .\数据库导出工具_简化版.exe
echo.
echo 注意: 简化版导出CSV格式文件，不是Excel格式
pause
