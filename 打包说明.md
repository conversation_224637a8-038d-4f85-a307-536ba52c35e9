# 数据库导出工具打包指南

## 打包方法

本项目提供了三种打包方式，您可以根据需要选择其中一种：

### 方法一：使用"打包程序.bat"（推荐）

1. 双击运行 `打包程序.bat`
2. 按任意键开始打包过程
3. 等待打包完成
4. 打包后的文件将保存在 `dist_YYYYMMDD_HHMMSS` 目录中

这种方法会自动处理所有打包步骤，并生成一个带时间戳的输出目录，包含 EXE 文件、配置文件和使用说明。

### 方法二：使用"快速打包.bat"

1. 双击运行 `快速打包.bat`
2. 按任意键开始打包过程
3. 等待打包完成
4. 打包后的文件将保存在 `dist` 目录中

这种方法直接使用 PyInstaller 命令行，适合快速打包。

### 方法三：手动运行 Python 脚本

1. 打开命令提示符或 PowerShell
2. 切换到项目目录
3. 运行命令：`python build_exe.py`
4. 等待打包完成
5. 打包后的文件将保存在 `dist_YYYYMMDD_HHMMSS` 目录中

## 打包后的文件

打包成功后，您将获得以下文件：

- `数据库导出工具.exe`：可执行文件，双击即可运行
- `config.ini`：配置文件，包含数据库连接信息和导出设置
- `使用说明.txt`：程序使用说明（仅方法一会生成）

## 分发方法

要分发给没有 Python 环境的用户，只需将以下文件复制到目标计算机：

1. `数据库导出工具.exe`
2. `config.ini`

确保这两个文件位于同一目录中。用户只需双击 EXE 文件即可运行程序。

## 注意事项

1. **配置文件**：确保 `config.ini` 文件中的数据库连接信息正确无误
2. **ODBC 驱动**：目标计算机需要安装 SQL Server ODBC 驱动程序
3. **防病毒软件**：某些防病毒软件可能会误报 PyInstaller 打包的 EXE 文件，可能需要添加例外
4. **密码保护**：程序使用了两级密码保护，确保用户知道正确的密码

## 常见问题

### 1. 程序无法启动

可能原因：
- 缺少 ODBC 驱动程序
- 防病毒软件拦截
- 配置文件丢失或损坏

解决方法：
- 安装 SQL Server ODBC 驱动程序
- 将程序添加到防病毒软件的白名单
- 确保配置文件与 EXE 文件在同一目录

### 2. 无法连接数据库

可能原因：
- 配置文件中的连接信息错误
- 网络问题
- 数据库服务器未运行

解决方法：
- 检查并修正配置文件中的连接信息
- 确保网络连接正常
- 确认数据库服务器正在运行

### 3. 打包过程失败

可能原因：
- PyInstaller 未正确安装
- Python 环境问题
- 依赖项缺失

解决方法：
- 重新安装 PyInstaller：`pip install --upgrade pyinstaller`
- 确保已安装 pyodbc：`pip install pyodbc`
- 检查 Python 版本是否兼容（推荐 Python 3.8 或更高版本）