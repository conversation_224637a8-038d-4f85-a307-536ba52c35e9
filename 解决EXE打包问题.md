# 解决EXE打包问题的完整方案

## 问题分析
您遇到的 `ModuleNotFoundError: No module named 'pyodbc'` 错误是因为PyInstaller在打包时没有正确包含pyodbc模块。

## 解决方案

### 方案1: 使用简化版程序（推荐）

1. **使用简化版程序**：
   ```bash
   python db_export_simple.py
   ```
   这个版本只依赖pyodbc，避免了pandas和openpyxl的复杂依赖问题。

2. **手动打包简化版**：
   ```bash
   # 清理旧文件
   rmdir /s /q dist
   rmdir /s /q build
   del *.spec
   
   # 安装依赖
   pip install --upgrade pyinstaller pyodbc
   
   # 打包
   pyinstaller --onefile --console db_export_simple.py
   
   # 重命名和复制配置
   ren "dist\db_export_simple.exe" "数据库导出工具.exe"
   copy config.ini dist\config.ini
   ```

### 方案2: 修复原版程序的打包

1. **创建requirements.txt**：
   ```
   pyodbc==4.0.34
   pandas==1.5.3
   openpyxl==3.0.10
   ```

2. **使用虚拟环境**：
   ```bash
   # 创建虚拟环境
   python -m venv venv
   
   # 激活虚拟环境
   venv\Scripts\activate
   
   # 安装依赖
   pip install -r requirements.txt
   pip install pyinstaller
   
   # 打包
   pyinstaller --onefile --console --hidden-import=pyodbc --hidden-import=pandas --hidden-import=openpyxl db_export_tool.py
   ```

3. **使用spec文件**：
   ```bash
   pyinstaller 数据库导出工具.spec
   ```

### 方案3: 手动解决依赖问题

1. **检查Python环境**：
   ```bash
   python -c "import pyodbc, pandas, openpyxl; print('所有模块可用')"
   ```

2. **如果缺少模块，安装**：
   ```bash
   pip install pyodbc pandas openpyxl
   ```

3. **使用最简单的打包命令**：
   ```bash
   pyinstaller --onefile --console --noupx db_export_tool.py
   ```

## 立即可用的解决方案

### 步骤1: 测试简化版程序
```bash
python db_export_simple.py
```

### 步骤2: 如果简化版工作正常，打包它
```bash
pyinstaller --onefile --console db_export_simple.py
ren "dist\db_export_simple.exe" "数据库导出工具.exe"
copy config.ini dist\config.ini
```

### 步骤3: 测试EXE文件
```bash
cd dist
.\数据库导出工具.exe
```

## 两个版本的区别

| 功能 | 原版 (db_export_tool.py) | 简化版 (db_export_simple.py) |
|------|---------------------------|-------------------------------|
| 数据库连接 | ✅ | ✅ |
| 密码验证 | ✅ | ✅ |
| 字符替换 | ✅ | ✅ |
| 导出格式 | Excel (.xlsx) | CSV (.csv) |
| 依赖库 | pyodbc, pandas, openpyxl | 仅 pyodbc |
| 打包难度 | 困难 | 简单 |

## 推荐流程

1. **先测试简化版**：确保功能正常
2. **打包简化版**：避免复杂依赖问题
3. **如果需要Excel格式**：可以用Excel打开CSV文件另存为xlsx

## 常见问题解决

### Q: 简化版能否导出Excel？
A: 简化版导出CSV格式，可以用Excel打开并另存为xlsx格式。

### Q: 字符替换功能是否正常？
A: 是的，简化版包含完整的字符替换功能。

### Q: 如果仍然出错怎么办？
A: 
1. 检查Python版本：`python --version`
2. 检查pyodbc安装：`python -c "import pyodbc; print('OK')"`
3. 使用虚拟环境重新安装所有依赖

## 最终建议

**立即使用简化版**：
1. 运行 `python db_export_simple.py` 测试功能
2. 如果正常，运行 `final_build.bat` 进行打包
3. 测试生成的EXE文件

简化版程序功能完整，只是导出格式为CSV而不是Excel，但CSV可以直接用Excel打开。
